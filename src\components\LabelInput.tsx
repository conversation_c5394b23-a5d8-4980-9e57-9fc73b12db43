import FormTooltip from "@/components/FormTooltip";
import { LabelInputProps } from "@/components/types";
import { AutosizeTextarea } from "@/components/ui/autosize-textarea";
import { useState } from "react";

const LabelInput = ({ label, required, tooltip, onLabelChange }: LabelInputProps) => {
  const [editMode, setEditMode] = useState<boolean>(false);

  const handleEditModeStart = () => {
    setEditMode(true);
    // Emit event that label input has started
    window.dispatchEvent(new CustomEvent('labelInputStart'));
  };

  const handleEditModeEnd = () => {
    setEditMode(false);
    // Emit event that label input has ended
    window.dispatchEvent(new CustomEvent('labelInputEnd'));
  };

  const handleLabelChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    // Emit event with the current label value
    window.dispatchEvent(new CustomEvent('labelInputChange', {
      detail: { label: e.target.value }
    }));
    // Call the original onChange handler
    onLabelChange(e);
  };

  return (
    <div className="flex items-start gap-2" onClick={handleEditModeStart}>
      {editMode ? (
        <AutosizeTextarea
          className="border-none bg-transparent p-0"
          placeholder="Question"
          value={label}
          onChange={handleLabelChange}
          autoFocus
          onBlur={handleEditModeEnd}
          onKeyDown={e => {
            if (e.key !== "Escape") return;
            handleEditModeEnd();
          }}
        ></AutosizeTextarea>
      ) : (
        <p className={`w-fit whitespace-pre-wrap ${label || "text-muted-foreground"}`}>{label || "Question"}</p>
      )}
      {required || <p className="text-primary-gray">(Optional)</p>}
      {tooltip && <FormTooltip description={tooltip} side="top" />}
    </div>
  );
};

export default LabelInput;
