import { audioRecordingPropertiesSchema } from "@/schemas/properties/audioRecordingProperties";
import { getBasicPropertiesSchema } from "@/schemas/properties/basicProperties";
import { cameraPropertiesSchema } from "@/schemas/properties/cameraProperties";
import { datePropertiesSchema } from "@/schemas/properties/dateProperties";
import { dropdownPropertiesSchema } from "@/schemas/properties/dropdownProperties";
import { filePropertiesSchema } from "@/schemas/properties/fileProperties";
import { multipleChoicePropertiesSchema } from "@/schemas/properties/multipleChoiceProperties";
import { numberPropertiesSchema } from "@/schemas/properties/numberProperties";
import { paragraphPropertiesSchema } from "@/schemas/properties/paragraphProperties";
import { phoneNumberPropertiesSchema } from "@/schemas/properties/phoneNumberProperties";
import { ratingPropertiesSchema } from "@/schemas/properties/ratingProperties";
import { shortAnswerPropertiesSchema } from "@/schemas/properties/shortAnswerProperties";
import { singleChoicePropertiesSchema } from "@/schemas/properties/singleChoiceProperties";
import { z } from "zod";

export type ExistingFormData = {
  data: SingleFormData[];
  organization: {
    id: string;
    name: string;
    logo: string;
  };
  currentPage: number;
  totalPages: number;
};
import { readOnlyPropertiesSchema } from "@/schemas/properties/readOnlyProperties";

export type SingleFormData = {
  name: string;
  description: string;
  audio: Audio;
  totalScreens: number;
  screens: Screen[];
  country: Country;
  isSubmitted: boolean;
  id: string;
  responseReceived: boolean;
  entity_type: string;
  created_at: string;
  updated_at: string;
  organization: {
    name: string;
    id: string;
    logo: string;
  };
};

export type Country = {
  name: string;
  country_code: string;
  flag: string;
  unicode_symbol: string;
  phone_code: string;
};

export type Audio = {
  name: string;
  url: string;
  format: string;
};

export type Screen = {
  id: string;
  name: string;
  description: string;
  sections: Section[];
};

export type Section = {
  id: string;
  title: string;
  components: Component[];
};

export type Component = {
  type: string;
  id: string;
  label?: string;
  title?: string;
  placeholder?: string;
  hint?: string;
  tooltip?: string;
  minimumCharacterCount?: number;
  maximumCharacterCount?: number;
  required?: boolean;
  validation?: Validation | null;
  minimumValue?: number;
  maximumValue?: number;
  minimumFileSize?: number;
  maximumFileSize?: number;
  file?: FormFile;
  mediaType?: string;
  validate?: boolean;
  currency?: boolean;
  countryCode?: string;
  minimumDate?: Date;
  maximumDate?: Date;
  dateFormat?: string;
  timeFormat?: string;
  optionType?: string;
  options?: string[];
  addOther?: boolean;
  addCheckAll?: boolean;
  allowMultipleFiles?: boolean;
  allowableDate?: string;
  minimumDurationCount?: Time;
  maximumDurationCount?: Time;
  numbersOfRating?: number;
  minimumDuration?: string;
  maximumDuration?: string;
  includeCountryCode?: boolean;
  isDecimal?: boolean;
  isPercentage?: boolean;
  isCorrectAnswerTicked?: boolean;
  correctAnswer?: string;
  lowestRating?: string;
  highestRating?: string;
  category?: string;
  fileUrl?: string;
  minimumDateRange?: string;
  maximumDateRange?: string;
  tag?: string;
};

export type Validation = {
  regex: string;
};

export type DecoupledFormElement = Component & Omit<Section, "id" | "components">;

export type FormFile = {
  type: string;
  formats: string[];
};
export type Time = {
  hours: number;
  minutes: number;
  seconds: number;
};

export type UpdateFormServerData = Partial<CreateFormServerData>;

export type CreateFormData = {
  formName: string;
  formDescription: string;
  audio?: File | string;
};

export type CreateFormServerData = {
  name: string;
  description?: string;
  audio?: File | string;
  screens: Screen[];
};

export type GenerateTagServerData = {
  label: string;
};

export type ModifiedAudioServerData = {
  name: string;
  url: string;
  format: string;
};

export type ExistingFormsQueryParams = {
  search: string;
  page: number;
  pageSize: number;
  sort?: number;
  organization_id: string;
};

export type BasicPropertiesFormData = z.infer<ReturnType<typeof getBasicPropertiesSchema>>;
export type ShortAnswerPropertiesFormData = z.infer<typeof shortAnswerPropertiesSchema>;
export type ParagraphPropertiesFormData = z.infer<typeof paragraphPropertiesSchema>;
export type NumberPropertiesFormData = z.infer<typeof numberPropertiesSchema>;
export type SingleChoicePropertiesFormData = z.infer<typeof singleChoicePropertiesSchema>;
export type MultipleChoicePropertiesFormData = z.infer<typeof multipleChoicePropertiesSchema>;
export type FilePropertiesFormData = z.infer<typeof filePropertiesSchema>;
export type CameraPropertiesFormData = z.infer<typeof cameraPropertiesSchema>;
export type PhoneNumberPropertiesFormData = z.infer<typeof phoneNumberPropertiesSchema>;
export type DatePropertiesFormData = z.infer<typeof datePropertiesSchema>;
export type AudioRecordingPropertiesFormData = z.infer<typeof audioRecordingPropertiesSchema>;
export type RatingPropertiesFormData = z.infer<typeof ratingPropertiesSchema>;
export type DropdownPropertiesFormData = z.infer<typeof dropdownPropertiesSchema>;
export type ReadOnlyPropertiesFormData = z.infer<typeof readOnlyPropertiesSchema>;
